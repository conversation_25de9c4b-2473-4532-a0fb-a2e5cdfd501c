name = "flux-krea"
compatibility_date = "2024-07-29"
compatibility_flags = ["nodejs_compat"]
pages_build_output_dir = ".vercel/output/static"

[vars]
# -----------------------------------------------------------------------------
# Web Information
# -----------------------------------------------------------------------------
NEXT_PUBLIC_WEB_URL = "https://flux-krea.dev"
NEXT_PUBLIC_PROJECT_NAME = "flux-krea.dev"

# -----------------------------------------------------------------------------
# Database with Supabase
# -----------------------------------------------------------------------------
# https://supabase.com/docs/guides/getting-started/quickstarts/nextjs
# Set your Supabase URL and Anon Key
SUPABASE_URL = "https://supabase.nancook.com"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NDk3Nzc0NTcsImV4cCI6MTg5MzQ1NjAwMCwiaXNzIjoiZG9rcGxveSJ9.LPewOYrTooydDgoBI5kBlkRvcX0JaLg41Z4soMty8z4"
SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpYXQiOjE3NDk3Nzc0NTcsImV4cCI6MTg5MzQ1NjAwMCwiaXNzIjoiZG9rcGxveSJ9.LPewOYrTooydDgoBI5kBlkRvcX0JaLg41Z4soMty8z4"

# -----------------------------------------------------------------------------
# Auth with next-auth
# https://authjs.dev/getting-started/installation?framework=Next.js
# Set your Auth URL and Secret
# Secret can be generated with `openssl rand -base64 32`
# -----------------------------------------------------------------------------
AUTH_SECRET = "Zt3BXVudzzRq2R2WBqhwRy1dNMq48Gg9zKAYq7YwSL0="

# Google Auth
# https://authjs.dev/getting-started/providers/google
AUTH_GOOGLE_ID = "1095502307789-p995qhj1k6r2dllovqmms0e67lp5njoo.apps.googleusercontent.com"
AUTH_GOOGLE_SECRET = "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"
NEXT_PUBLIC_AUTH_GOOGLE_ID = "1095502307789-p995qhj1k6r2dllovqmms0e67lp5njoo.apps.googleusercontent.com"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED = "true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED = "true"

# Github Auth
# https://authjs.dev/getting-started/providers/github
AUTH_GITHUB_ID = ""
AUTH_GITHUB_SECRET = ""
NEXT_PUBLIC_AUTH_GITHUB_ENABLED = "false"

# -----------------------------------------------------------------------------
# Analytics with Google Analytics
# https://analytics.google.com
# -----------------------------------------------------------------------------
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID = "G-2NJSK3053G"

# -----------------------------------------------------------------------------
# Analytics with OpenPanel
# https://openpanel.dev
# -----------------------------------------------------------------------------
NEXT_PUBLIC_OPENPANEL_CLIENT_ID = ""

# -----------------------------------------------------------------------------
# Payment with Stripe
# https://docs.stripe.com/keys
# -----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY = "pk_live_51RrPGPAeDL1p2QJuMKmfv6uuxXow1L3AGcZVt16L8RFM4cACXhcpmoQKXuAURVMUFkhshljYMTasUDOoiuVdSgyB00bRTpmY2C"
STRIPE_PRIVATE_KEY = "***********************************************************************************************************"
STRIPE_WEBHOOK_SECRET = "whsec_O0B9B2WTBPTKz0xL9kUpqOSfc1u1WuzN"

NEXT_PUBLIC_PAY_SUCCESS_URL = "https://flux-krea.dev/my-orders"
NEXT_PUBLIC_PAY_FAIL_URL = "https://flux-krea.dev/pricing"
NEXT_PUBLIC_PAY_CANCEL_URL = "https://flux-krea.dev/pricing"

NEXT_PUBLIC_LOCALE_DETECTION = "false"

ADMIN_EMAILS = "<EMAIL>"

NEXT_PUBLIC_DEFAULT_THEME = "light"

# -----------------------------------------------------------------------------
# Storage with aws s3 sdk，R2
# https://docs.aws.amazon.com/s3/index.html
# -----------------------------------------------------------------------------
STORAGE_ENDPOINT = "https://b8617f91531e25bf5bfb036b5cca1ada.r2.cloudflarestorage.com"
STORAGE_REGION = "auto"
STORAGE_ACCESS_KEY = "afa9c9e31ce9a45015f5db38b04b8c19"
STORAGE_SECRET_KEY = "aeef76587eecba288435b3389ed3f518b5d210348f6967c5dab0a46e5f8b3e33"
STORAGE_BUCKET = "fluxkreadev"
STORAGE_DOMAIN = "https://img.flux-krea.dev"

REPLICATE_API_TOKEN = "****************************************"

# Kie.ai API配置
KIE_API_KEY="246f3ffbf0e6cb0023ae921699963ab6"
NEXT_PUBLIC_APP_URL="https://flux-krea.dev"
DEEPSEEK_API_KEY="***********************************"





