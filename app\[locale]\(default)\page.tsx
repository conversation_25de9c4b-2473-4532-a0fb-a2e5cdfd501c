import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import HeroWithCompare from "@/components/blocks/hero-with-compare";
import ImageCompareGallery from "@/components/blocks/image-compare-gallery";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import { Generator4o } from "@/components/generator4o";
import { Kontext } from "@/components/kontext";
import KontextDev from "@/components/kontextdev";
import FluxKreaDev from "@/components/fluxkreadev";
import { Button } from "@/components/ui/button";
import Link from "next/link";


export const runtime = "edge";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function LandingPage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);

  // These can be updated with the URLs provided by the user
  const compareImages = {
    originalSrc: "https://pic.flux-krea.dev/flux-krea-woman-portrait.webp",
    modifiedSrc: "https://pic.flux-krea.dev/flux-dev-woman-portrait.webp",
    beforeText: "Flux Krea Generated",
    afterText: "Flux Dev Generated"
  };

  // 示例图片对比数据 - 这些应该替换为实际的图片URL
  const compareGroups = [
    {
      id: 1,
      originalSrc: "https://pic.flux-krea.dev/flux-krea-girl-portrait.jpg",
      modifiedSrc: "https://pic.flux-krea.dev/flux-dev-girl-portrait.jpg",
      alt: "A front-facing portrait of a girl, Generated by flux krea",
      beforeText: "Flux Krea Generated",
      afterText: "Flux Dev Generated"
    },
    {
      id: 2,
      originalSrc: "https://pic.flux-krea.dev/flux-krea-orange-cat-on-box.jpg",
      modifiedSrc: "https://pic.flux-krea.dev/flux-dev-orange-cat-on-box.jpg",
      alt: "Orange cat lying on a cardboard box, generated by Flux Krea",
      beforeText: "Flux Krea Generated",
      afterText: "Flux Dev Generated"
    },
    {
      id: 3,
      originalSrc: "https://pic.flux-krea.dev/flux-krea-van-gogh-style-painting.jpg",
      modifiedSrc: "https://pic.flux-krea.dev/flux-dev-van-gogh-style-painting.jpg",
      alt: "Van Gogh-style oil painting generated by Flux Krea",
      beforeText: "Flux Krea Generated",
      afterText: "Flux Dev Generated"
    },
    {
      id: 4,
      originalSrc: "https://pic.flux-krea.dev/flux-krea-close-up-eye.jpg",
      modifiedSrc: "https://pic.flux-krea.dev/flux-dev-close-up-eye.jpg",
      alt: "Close-up of a human eye generated by Flux Krea",
      beforeText: "Flux Krea Generated",
      afterText: "Flux Dev Generated"
    },
    {
      id: 5,
      originalSrc: "https://pic.flux-krea.dev/flux-krea-church-hall-interior.jpg",
      modifiedSrc: "https://pic.flux-krea.dev/flux-dev-church-hall-interior.jpg",
      alt: "Interior view of a church hall generated by Flux Krea",
      beforeText: "Flux Krea Generated",
      afterText: "Flux Dev Generated"
    },
    {
      id: 6,
      originalSrc: "https://pic.flux-krea.dev/flux-krea-angel-fresco-style.jpg",
      modifiedSrc: "https://pic.flux-krea.dev/flux-dev-angel-fresco-style.jpg",
      alt: "Angel wall painting generated by Flux Krea",
      beforeText: "Flux Krea Generated",
      afterText: "Flux Dev Generated"
    }
  ];

  return (
    <>
      {page.hero && <HeroWithCompare
        hero={page.hero}
        compareImages={compareImages}
      />}
      <FluxKreaDev />
      
      
      
      
      {/* 图片对比画廊 */}
      <ImageCompareGallery 
        title="Flux Krea vs Flux.1 [dev]: See the Photorealistic Difference"
        description="Compare the same prompt generated by both models. Flux.1 Krea [dev] delivers superior photorealism with natural skin tones, balanced lighting, and authentic details - while standard Flux.1 [dev] shows typical AI artifacts. Experience the clear advantage of our enhanced model."
        compareGroups={compareGroups} 
      />

      {page.branding && <Branding section={page.branding} />}
      {page.introduce && <Feature1 section={page.introduce} />}
      {page.benefit && <Feature2 section={page.benefit} />}
      {page.usage && <Feature3 section={page.usage} />}
      
      
      
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.stats && <Stats section={page.stats} />}
      
      {/* Pricing Section - Just a button to pricing page */}
      <div className="container py-12 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold tracking-tight mb-4">Ready to Get Started?</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Explore our pricing plans and find the perfect option for your creative needs
          </p>
          <Link href="/pricing">
            <Button size="lg" className="gap-2">
              View Pricing Plans
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M5 12h14" />
                <path d="m12 5 7 7-7 7" />
              </svg>
            </Button>
          </Link>
        </div>
      </div>
      
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
      {page.cta && <CTA section={page.cta} />}
      
    </>
  );
}
