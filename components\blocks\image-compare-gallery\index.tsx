"use client";

import React from 'react';
import { ImageCompare } from '@/components/image-compare';
import { Badge } from "@/components/ui/badge";

// 定义图片对比组的类型
interface CompareGroup {
  id: number;
  originalSrc: string;
  modifiedSrc: string;
  alt: string;
  beforeText?: string;
  afterText?: string;
}

interface ImageCompareGalleryProps {
  title?: string;
  description?: string;
  compareGroups: CompareGroup[];
}

export default function ImageCompareGallery({
  title = "Before & After Gallery",
  description = "See the magic of our Ghibli AI transformation with these before and after comparisons.",
  compareGroups = []
}: ImageCompareGalleryProps) {
  // 如果没有提供对比组，则不渲染组件
  if (compareGroups.length === 0) {
    return null;
  }

  return (
    <section className="py-16 photo-restore-section">
      <div className="container px-4">
        <div className="text-center mb-12">
          <Badge variant="outline" className="mb-4">
            Flux Krea & Flux Dev
          </Badge>
          <h2 className="text-3xl font-bold mb-4 ghibli-heading">{title}</h2>
          <p className="max-w-2xl mx-auto text-muted-foreground">
            {description}
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {compareGroups.map((group) => (
            <div key={group.id} className="flex flex-col items-center">
              <ImageCompare 
                originalSrc={group.originalSrc}
                modifiedSrc={group.modifiedSrc}
                alt={group.alt}
                beforeText={group.beforeText || "Original"}
                afterText={group.afterText || "Edited with Kontext Dev"}
                className="w-full max-w-2xl photo-restore-card p-3"
              />
              <p className="mt-4 text-base text-center text-muted-foreground font-medium">
                {group.alt}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
} 