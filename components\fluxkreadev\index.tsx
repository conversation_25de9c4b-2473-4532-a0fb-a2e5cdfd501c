"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import Image from "next/image";
import { v4 as uuidv4 } from "uuid";
import { UserCredits } from "@/types/user";
import { X, Download } from "lucide-react";
import { UpgradeModal } from "@/components/ui/upgrade-modal";

interface FluxKreaDevProps {
  title?: string;
  subtitle?: string;
}

export default function FluxKreaDev({
  title = "Flux Krea Dev AI Image Generator",
  subtitle = "Select a style, type to get your own flux ai image"
}: FluxKreaDevProps = {}) {
  // Session and mounting state
  const { data: session } = useSession();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [userCredits, setUserCredits] = useState<number | null>(null);

  // Form states
  const [prompt, setPrompt] = useState("Three adults are dressed in colorful animal costumes at a club, including a blue bunny with a white cloud pattern, a pink bunny with white polka dots, and a yellow bunny with pink ears, all smiling and posing for a photo");
  const [aspectRatio, setAspectRatio] = useState("1:1");
  const [seed, setSeed] = useState("");
  const [watermark, setWatermark] = useState(true);
  const [displayPublic, setDisplayPublic] = useState(true);
  const [imageCount, setImageCount] = useState(1);

  // Generation states
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedImages, setGeneratedImages] = useState<string[]>([]);

  const [generatedSeed, setGeneratedSeed] = useState<number | null>(null);
  const [progress, setProgress] = useState(0);
  const [progressPhase, setProgressPhase] = useState<'waiting' | 'generating' | 'completed'>('waiting');
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  // Check user status when component loads
  useEffect(() => {
    async function checkUserStatus() {
      try {
        const response = await fetch('/api/credits');
        if (response.ok) {
          const userCredits: UserCredits = await response.json();
          setUserCredits(userCredits.left_credits);
        }
      } catch (error) {
        console.error('Failed to check user status:', error);
      }
    }

    setMounted(true);
    checkUserStatus();
  }, []);

  // Check session status when component loads
  useEffect(() => {
    if (session) {
      fetchUserCredits();
    }
  }, [session]);

  // Refresh credits after successful generation
  useEffect(() => {
    if (generatedImages.length > 0 && session) {
      fetchUserCredits();
    }
  }, [generatedImages, session]);

  const fetchUserCredits = async () => {
    try {
      const response = await fetch('/api/credits');
      if (!response.ok) {
        throw new Error('Failed to fetch user credits');
      }
      const data: UserCredits = await response.json();
      setUserCredits(data.left_credits);
    } catch (error) {
      console.error('Error fetching user credits:', error);
      setUserCredits(0);
    }
  };

  const handleGenerate = async () => {
    if (!session) {
      toast.error('Please sign in to generate images');
      router.push('/auth/signin');
      return;
    }

    if (!prompt.trim()) {
      toast.error('Please enter a prompt');
      return;
    }

    // Check if user has enough credits
    if (userCredits !== null && userCredits < 3) {
      toast.error('Insufficient credits. Each image generation requires 3 credits');
      router.push('/pricing');
      return;
    }

    setIsGenerating(true);
    setGeneratedSeed(null); // Reset seed for new generation
    setProgress(0);
    setProgressPhase('waiting');
    toast.info('Preparing image generation...', { duration: 3000 });

    try {
      // Phase 1: 40-second waiting period with progress
      const waitDuration = 40000; // 40 seconds
      const progressInterval = 100; // Update every 100ms
      const totalSteps = waitDuration / progressInterval;

      for (let step = 0; step <= totalSteps; step++) {
        await new Promise(resolve => setTimeout(resolve, progressInterval));
        const waitProgress = Math.min((step / totalSteps) * 80, 80); // 80% for waiting phase
        setProgress(waitProgress);
      }

      // Phase 2: Start actual generation
      setProgressPhase('generating');
      setProgress(85);
      toast.info('Generating image...', { duration: 3000 });

      // Generate unique task ID
      const taskId = `fluxkrea_${uuidv4()}`;

      const response = await fetch('/api/generate/flux-krea', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt.trim(),
          aspect_ratio: aspectRatio,
          seed: seed || undefined,
          watermark,
          display_public: displayPublic,
          image_count: imageCount,
          task_id: taskId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Generation failed');
      }

      const data = await response.json();

      if (data.images && data.images.length > 0) {
        // Phase 3: Completion
        setProgress(100);
        setProgressPhase('completed');

        setGeneratedImages(data.images);
        setGeneratedSeed(data.seed || null);
        toast.success('Image generated successfully!');

        // Refresh user credits
        await fetchUserCredits();

        // Reset progress after a short delay
        setTimeout(() => {
          setProgress(0);
          setProgressPhase('waiting');
        }, 2000);
      } else {
        throw new Error('No images generated');
      }
    } catch (error) {
      console.error('Generation error:', error);
      toast.error(error instanceof Error ? error.message : 'Generation failed');
      setProgress(0);
      setProgressPhase('waiting');
    } finally {
      setIsGenerating(false);
    }
  };

  const aspectRatioOptions = [
    { value: "16:9", label: "16:9", width: 16, height: 9 },
    { value: "3:2", label: "3:2", width: 3, height: 2 },
    { value: "4:3", label: "4:3", width: 4, height: 3 },
    { value: "1:1", label: "1:1", width: 1, height: 1 },
    { value: "4:5", label: "4:5", width: 4, height: 5 },
    { value: "5:4", label: "5:4", width: 5, height: 4 },
    { value: "3:4", label: "3:4", width: 3, height: 4 },
    { value: "2:3", label: "2:3", width: 2, height: 3 },
    { value: "9:16", label: "9:16", width: 9, height: 16 },
    { value: "21:9", label: "21:9", width: 21, height: 9 }
  ];

  // 计算比例示意框的尺寸
  const getRatioBoxStyle = (width: number, height: number) => {
    const maxSize = 28; // 最大尺寸
    const ratio = width / height;

    let boxWidth, boxHeight;
    if (ratio > 1) {
      // 宽度大于高度
      boxWidth = maxSize;
      boxHeight = maxSize / ratio;
    } else {
      // 高度大于等于宽度
      boxHeight = maxSize;
      boxWidth = maxSize * ratio;
    }

    return {
      width: `${Math.round(boxWidth)}px`,
      height: `${Math.round(boxHeight)}px`
    };
  };

  const imageCountOptions = [1, 2, 3, 4, 5];

  // Handle premium feature toggles
  const handleWatermarkToggle = (checked: boolean) => {
    // If trying to turn off watermark (checked = false), check premium access
    if (!checked) {
      if (!session || (userCredits !== null && userCredits < 5)) {
        setShowUpgradeModal(true);
        return;
      }
    }
    setWatermark(checked);
  };

  const handleDisplayPublicToggle = (checked: boolean) => {
    // If trying to turn off display public (checked = false), check premium access
    if (!checked) {
      if (!session || (userCredits !== null && userCredits < 5)) {
        setShowUpgradeModal(true);
        return;
      }
    }
    setDisplayPublic(checked);
  };

  return (
    <section className="py-12 bg-background">
      <div className="w-full max-w-6xl mx-auto p-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-2">{title}</h2>
          <p className="text-lg text-muted-foreground">{subtitle}</p>
        </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Left panel */}
        <Card className="p-6 space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold">Flux Krea Settings</h3>
            <div className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full flex items-center gap-2">
              <span className="text-sm font-normal">Credits:</span>{" "}
              {mounted && session ? (
                <span className="text-lg font-bold">{userCredits !== null ? userCredits : "Loading..."}</span>
              ) : (
                <span className="text-lg font-bold">
                  <a href="/auth/signin" className="hover:underline">Sign in</a>
                </span>
              )}
              <svg
                className="w-4 h-4 text-amber-500 fill-current"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
              >
                <path d="M12 17a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0-15c-4.97 0-9 4.03-9 9v7c0 1.66 1.34 3 3 3h12c1.66 0 3-1.34 3-3v-7c0-4.97-4.03-9-9-9zm0 3c3.31 0 6 2.69 6 6v7c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1v-7c0-3.31 2.69-6 6-6z" />
              </svg>
            </div>
          </div>

          {/* Unlogged user prompt */}
          {mounted && !session && (
            <div className="text-center py-2 bg-blue-50 rounded-lg mb-4">
              <a
                href="/auth/signin"
                className="text-blue-600 hover:text-blue-800 underline text-sm"
              >
                Sign in to start generating images
              </a>
            </div>
          )}

          {/* Prompt */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="prompt" className="text-sm font-medium">Prompt</Label>
              {prompt && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setPrompt('')}
                  className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
                  title="Clear prompt"
                >
                  <X className="h-3 w-3" />
                  Clear
                </Button>
              )}
            </div>
            <Textarea
              id="prompt"
              placeholder="Describe the image you want to generate..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="min-h-[120px] resize-y text-sm"
            />
          </div>

          {/* Image Dimensions */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Image Dimensions</Label>
            <div className="grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 gap-2">
              {aspectRatioOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={aspectRatio === option.value ? "default" : "outline"}
                  className={`h-16 sm:h-18 lg:h-20 aspect-square flex flex-col items-center justify-center p-2 sm:p-3 text-xs gap-1 sm:gap-2 transition-all ${
                    aspectRatio === option.value
                      ? "bg-blue-600 text-white border-blue-600"
                      : "bg-white hover:bg-gray-50 border-gray-200"
                  }`}
                  onClick={() => setAspectRatio(option.value)}
                >
                  {/* 比例示意框 */}
                  <div
                    className={`border-2 rounded-sm flex-shrink-0 bg-transparent ${
                      aspectRatio === option.value
                        ? "border-white"
                        : "border-gray-400"
                    }`}
                    style={getRatioBoxStyle(option.width, option.height)}
                  />
                  {/* 比例文字 */}
                  <span className="text-xs font-medium">{option.label}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Seed */}
          <div className="space-y-2">
            <Label htmlFor="seed" className="text-sm">Seed (Optional)</Label>
            <Input
              id="seed"
              type="number"
              placeholder="Random seed for reproducible results"
              value={seed}
              onChange={(e) => setSeed(e.target.value)}
              className="text-sm"
            />
          </div>

          {/* Watermark */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Label htmlFor="watermark" className="text-sm">Watermark</Label>
              {!watermark && (
                <div className="w-3 h-3">
                  <svg viewBox="0 0 24 24" fill="#FFD700" className="w-full h-full">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
              )}
            </div>
            <Switch
              id="watermark"
              checked={watermark}
              onCheckedChange={handleWatermarkToggle}
            />
          </div>

          {/* Display Public */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Label htmlFor="display-public" className="text-sm">Display Public</Label>
              {!displayPublic && (
                <div className="w-3 h-3">
                  <svg viewBox="0 0 24 24" fill="#FFD700" className="w-full h-full">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                </div>
              )}
            </div>
            <Switch
              id="display-public"
              checked={displayPublic}
              onCheckedChange={handleDisplayPublicToggle}
            />
          </div>

          {/* Number of Images */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Number of Images</Label>
            <p className="text-xs text-muted-foreground">Select how many images to generate</p>
            <div className="flex gap-2">
              {imageCountOptions.map((count) => (
                <Button
                  key={count}
                  variant={imageCount === count ? "default" : "outline"}
                  size="sm"
                  className="w-8 h-8 text-xs relative"
                  onClick={() => {
                    if (count > 1) {
                      // Check if user is logged in and has enough credits for premium features
                      if (!session || (userCredits !== null && userCredits < 5)) {
                        setShowUpgradeModal(true);
                        return;
                      }
                    }
                    setImageCount(count);
                  }}
                >
                  {count}
                  {count > 1 && (
                    <div className="absolute -top-1 -right-1 w-3 h-3">
                      <svg viewBox="0 0 24 24" fill="#FFD700" className="w-full h-full">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                      </svg>
                    </div>
                  )}
                </Button>
              ))}
            </div>
          </div>

          {/* Progress Bar */}
          {isGenerating && (
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">
                  {progressPhase === 'waiting' && 'Preparing...'}
                  {progressPhase === 'generating' && 'Generating...'}
                  {progressPhase === 'completed' && 'Completed!'}
                </span>
                <span className="text-sm text-gray-500">{Math.round(progress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <div className="text-center">
                <p className="text-sm text-red-600 font-medium">
                  ⚠️Please don't close the page. Your image is being generated. Thank you for your patience!
                </p>
              </div>
            </div>
          )}

          {/* Generate Button */}
          <Button
            onClick={handleGenerate}
            disabled={isGenerating || !prompt.trim()}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium"
            size="lg"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Generating...
              </>
            ) : (
              <>
                <span className="mr-2">🎨</span>
                Generate Image {imageCount > 1 ? `(${imageCount})` : ''} 3 Credit 💎
              </>
            )}
          </Button>

          {/* Contact */}
          <div className="text-center text-sm text-muted-foreground">
            <span>Contact: </span>
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>
          </div>
        </Card>

        {/* Right panel */}
        <Card className="p-6 space-y-4">
          <h3 className="text-xl font-semibold">Flux Krea Results</h3>

          {generatedImages.length > 0 ? (
            <div className="space-y-6">
              {generatedImages.map((imageUrl, index) => (
                <div key={index} className="border rounded-lg p-4 bg-white">
                  {/* Image title */}
                  <h4 className="text-lg font-medium mb-3">Image {index + 1}</h4>

                  {/* Image display */}
                  <div
                    className="relative aspect-[4/3] w-full bg-muted rounded-lg overflow-hidden cursor-pointer hover:ring-2 hover:ring-blue-500 transition-all mb-4"
                    onClick={() => {
                      window.open(imageUrl, '_blank');
                    }}
                    title={`Click to open image ${index + 1} in new window`}
                  >
                    <Image
                      src={imageUrl}
                      alt={`Generated image ${index + 1}`}
                      fill
                      className="object-contain"
                      priority={index === 0}
                    />
                  </div>

                  {/* Seed display for this image */}
                  {generatedSeed && (
                    <div className="bg-gray-50 rounded-lg p-3 border mb-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">Seed:</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-mono text-gray-900">{generatedSeed}</span>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={() => {
                              navigator.clipboard.writeText(generatedSeed.toString());
                              toast.success(`Seed for Image ${index + 1} copied to clipboard!`);
                            }}
                          >
                            Copy
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Download button for this image */}
                  <Button
                    className="w-full"
                    size="lg"
                    onClick={async () => {
                      try {
                        console.log(`Starting download for image ${index + 1}...`);
                        const urlToDownload = imageUrl;
                        console.log("Original download URL:", urlToDownload.substring(0, 100) + "...");

                        if (!urlToDownload) {
                          console.error("No image URL available");
                          toast.error('No image URL available');
                          return;
                        }

                        // Use image proxy API to download, avoiding CORS issues
                        console.log("Preparing to download image via proxy...");
                        toast.info("Preparing download...", { duration: 2000 });

                        // Use proxy API for download
                        const proxyUrl = `/api/proxy-image?url=${encodeURIComponent(urlToDownload)}`;
                        const link = document.createElement('a');
                        link.href = proxyUrl;
                        link.download = `flux-krea-image-${index + 1}.png`;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        console.log("Download initiated successfully");
                        toast.success("Download started!");
                      } catch (error) {
                        console.error("Download failed:", error);
                        toast.error("Failed to download image");
                      }
                    }}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download Image {index + 1}
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="relative aspect-[4/3] w-full bg-muted rounded-lg overflow-hidden">
              <div className="absolute inset-0 flex items-center justify-center">
                <p className="text-muted-foreground">
                  {isGenerating ? 'Generating...' : 'Generated images will appear here'}
                </p>
              </div>
            </div>
          )}




        </Card>
      </div>
      </div>

      {/* Upgrade Modal */}
      <UpgradeModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        onUpgrade={() => {
          setShowUpgradeModal(false);
          router.push('/pricing');
        }}
      />
    </section>
  );
}
