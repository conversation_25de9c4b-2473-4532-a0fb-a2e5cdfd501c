import { NextResponse } from "next/server";
import Replicate from "replicate";
import { v4 as uuidv4 } from 'uuid';
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSupabaseClient } from "@/models/db";
import { getUserUuid } from "@/services/user";
import { decreaseCredits, CreditsTransType, getUserCredits, increaseCredits } from "@/services/credit";

export const runtime = 'edge';

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN,
});

// 创建S3客户端用于R2上传
const s3Client = new S3Client({
  region: "auto",
  endpoint: process.env.STORAGE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: process.env.STORAGE_SECRET_KEY || "",
  },
  forcePathStyle: true
});

interface Prediction {
  id: string;
  status: string;
  error?: string;
  output?: string[] | string;
  input?: any;
  logs?: string;
}

// 从URL下载图像并上传到R2
async function uploadImageToR2(imageUrl: string, filename: string): Promise<string> {
  try {
    // 下载图像
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText}`);
    }
    
    // 获取图像数据并转换为Uint8Array
    const imageBuffer = await response.arrayBuffer();
    const uint8Array = new Uint8Array(imageBuffer);
    
    // 上传到R2
    const uploadCommand = new PutObjectCommand({
      Bucket: process.env.STORAGE_BUCKET || "",
      Key: filename,
      Body: uint8Array,
      ContentType: 'image/png',
    });

    await s3Client.send(uploadCommand);
    
    // 构建新的URL
    const newUrl = `https://img.flux-krea.dev/${filename}`;
    
    return newUrl;
  } catch (error) {
    // 上传失败但不中断整个流程，返回原始URL
    console.log('Falling back to original URL:', imageUrl);
    return imageUrl;
  }
}



// 退还积分函数
async function refundCredits(userUuid: string, credits: number, taskId: string, reason: string) {
  try {
    console.log(`Refunding ${credits} credits for user ${userUuid}, task ${taskId}, reason: ${reason}`);

    await increaseCredits({
      user_uuid: userUuid,
      trans_type: "system_add", // 使用系统添加类型表示退还
      credits: credits,
      expired_at: "", // 退还的积分不设置过期时间
      order_no: `refund_${taskId}` // 使用任务ID作为订单号标识
    });

    console.log(`Successfully refunded ${credits} credits for task ${taskId}`);
  } catch (error) {
    console.error(`Failed to refund credits for task ${taskId}:`, error);
    // 不抛出错误，避免影响主流程
  }
}

export async function POST(req: Request) {
  try {
    const { 
      prompt, 
      aspect_ratio = "1:1", 
      seed, 
      watermark = true, 
      display_public = true, 
      image_count = 1, 
      task_id: requestTaskId 
    } = await req.json();

    if (!prompt) {
      return NextResponse.json(
        { error: "Prompt is required", status: 'failed', taskId: requestTaskId },
        { status: 400 }
      );
    }

    // 获取用户ID
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return NextResponse.json(
        { error: "User authentication required", status: 'failed', taskId: requestTaskId },
        { status: 401 }
      );
    }

    // 确保有有效的任务ID
    const taskId = requestTaskId || `fluxkrea_${uuidv4()}`;

    // 检查用户积分
    const userCredits = await getUserCredits(userUuid);
    const requiredCredits = 3 * image_count; // 每张图片需要3积分

    if (userCredits.left_credits < requiredCredits) {
      return NextResponse.json(
        {
          error: `Insufficient credits. Required: ${requiredCredits}, Available: ${userCredits.left_credits}`,
          status: 'failed',
          taskId: taskId
        },
        { status: 402 }
      );
    }

    // 扣除积分
    try {
      await decreaseCredits({
        user_uuid: userUuid,
        trans_type: CreditsTransType.ImageGeneration,
        credits: requiredCredits
      });
      console.log(`Credits deducted successfully: ${requiredCredits}`);
    } catch (creditError) {
      console.error('Failed to deduct credits:', creditError);
      return NextResponse.json(
        {
          error: "Failed to deduct credits",
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    console.log('Starting Flux Krea generation with params:', {
      task_id: taskId,
      prompt: prompt,
      aspect_ratio: aspect_ratio,
      seed: seed,
      watermark: watermark,
      display_public: display_public,
      image_count: image_count,
      credits_deducted: requiredCredits
    });

    // 注意：我们将在生成完成后保存所有图片的记录

    // 创建预测任务 - 使用 Flux Krea Dev 模型
    let prediction;
    try {
      const input: any = {
        prompt: prompt,
        go_fast: true,
        guidance: 3,
        megapixels: "1",
        num_outputs: image_count,
        aspect_ratio: aspect_ratio,
        output_format: "png",
        output_quality: 80,
        prompt_strength: 0.8,
        num_inference_steps: 28
      };

      // 如果提供了种子，添加到输入中
      if (seed && seed.trim()) {
        input.seed = parseInt(seed);
      }

      prediction = await replicate.predictions.create({
        model: "black-forest-labs/flux-krea-dev",
        input: input
      }) as Prediction;
    } catch (replicateError) {
      console.error('Replicate API error:', replicateError);

      // 退还积分
      await refundCredits(userUuid, requiredCredits, taskId, "Replicate API connection failed");

      return NextResponse.json(
        {
          error: replicateError instanceof Error ? replicateError.message : "Failed to connect to generation service",
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    // 轮询检查预测状态
    let finalPrediction = prediction;
    try {
      while (finalPrediction.status !== "succeeded" && finalPrediction.status !== "failed") {
        await new Promise(resolve => setTimeout(resolve, 1000));
        finalPrediction = await replicate.predictions.get(prediction.id) as Prediction;
      }
    } catch (pollingError) {
      console.error('轮询状态错误:', pollingError);

      // 退还积分
      await refundCredits(userUuid, requiredCredits, taskId, "Failed to check generation status");

      return NextResponse.json(
        {
          error: "Failed to check generation status",
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    if (finalPrediction.status === "failed") {
      const errorMessage = finalPrediction.error || "Generation failed";
      console.error('Generation failed:', errorMessage);

      // 退还积分
      await refundCredits(userUuid, requiredCredits, taskId, `Generation failed: ${errorMessage}`);

      return NextResponse.json(
        {
          error: errorMessage,
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    // 处理输出 URL
    let outputUrls: string[] = [];
    
    if (Array.isArray(finalPrediction.output)) {
      outputUrls = finalPrediction.output.filter(url => typeof url === 'string' && url.startsWith('http'));
    } else if (typeof finalPrediction.output === 'string' && finalPrediction.output.startsWith('http')) {
      outputUrls = [finalPrediction.output];
    }

    if (outputUrls.length === 0) {
      // 退还积分
      await refundCredits(userUuid, requiredCredits, taskId, "No valid output generated");

      return NextResponse.json(
        {
          error: "No valid output generated",
          status: 'failed',
          taskId: taskId
        },
        { status: 500 }
      );
    }

    // 上传图像到 R2 存储
    const uploadedUrls: string[] = [];
    const originalUrls: string[] = [];

    for (let i = 0; i < outputUrls.length; i++) {
      const outputUrl = outputUrls[i];
      originalUrls.push(outputUrl);
      
      // 生成文件名
      const uuid = uuidv4();
      const filename = `flux-krea-dev-${uuid}-${i + 1}.png`;
      
      try {
        const r2ImageUrl = await uploadImageToR2(outputUrl, filename);
        uploadedUrls.push(r2ImageUrl);
      } catch (uploadError) {
        console.error(`Upload failed for image ${i + 1}:`, uploadError);
        uploadedUrls.push(outputUrl); // 使用原始URL作为回退
      }
    }

    // 为每张图片创建数据库记录
    try {
      const supabase = getSupabaseClient();

      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("nickname, email")
        .eq("uuid", userUuid)
        .single();

      if (userError) {
        console.error('Error getting user data for record update:', userError);
      }

      // 为每张图片创建一条记录
      const recordsToInsert = uploadedUrls.map((imageUrl, index) => ({
        user_uuid: userUuid,
        task_id: `${taskId}_${index + 1}`, // 为每张图片创建唯一的task_id
        callback_id: `callback_${uuidv4()}`,
        prompt: `${prompt} [Image ${index + 1}/${uploadedUrls.length}]`, // 在prompt中标记图片序号
        generated_image_url: imageUrl,
        original_image_url: 'flux-krea-text-to-image', // Flux Krea 是文本到图像
        status: 'COMPLETED',
        created_at: new Date().toISOString(),
        completed_at: new Date().toISOString(),
        nickname: userData?.nickname || null,
        email: userData?.email || null
      }));

      const { error: insertError } = await supabase
        .from("4o_generations")
        .insert(recordsToInsert);

      if (insertError) {
        console.error('Error inserting generation records:', insertError);
      } else {
        console.log(`Successfully inserted ${uploadedUrls.length} generation records`);
      }
    } catch (dbError) {
      console.error('Failed to save generation records:', dbError);
    }

    // 提取seed值
    let usedSeed = null;

    // 尝试从input中获取seed
    if (finalPrediction.input && finalPrediction.input.seed) {
      usedSeed = finalPrediction.input.seed;
    }

    // 如果input中没有seed，尝试从logs中解析
    if (!usedSeed && finalPrediction.logs) {
      const seedMatch = finalPrediction.logs.match(/Using seed: (\d+)/);
      if (seedMatch) {
        usedSeed = parseInt(seedMatch[1]);
      }
    }

    console.log('Used seed for generation:', usedSeed);

    // 返回结果
    return NextResponse.json({
      status: 'succeeded',
      images: uploadedUrls,
      original_images: originalUrls,
      taskId: taskId,
      seed: usedSeed
    });

  } catch (error) {
    console.error("Flux Krea generation error:", error);

    // 尝试从请求中获取参数进行积分退还
    try {
      const requestBody = await req.json();
      const taskId = requestBody.taskId || null;
      const imageCount = requestBody.image_count || 1;
      const requiredCredits = 3 * imageCount;

      if (taskId) {
        const userUuid = await getUserUuid();
        if (userUuid) {
          await refundCredits(userUuid, requiredCredits, taskId, `Unexpected error: ${error instanceof Error ? error.message : "Unknown error"}`);
        }
      }
    } catch (refundError) {
      console.error("Failed to refund credits in catch block:", refundError);
    }

    const taskId = (await req.json()).taskId || null;
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Failed to generate image",
        status: 'failed',
        taskId: taskId
      },
      { status: 500 }
    );
  }
}
