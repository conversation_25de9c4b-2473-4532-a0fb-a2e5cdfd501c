"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpgrade?: () => void;
}

export function UpgradeModal({ isOpen, onClose, onUpgrade }: UpgradeModalProps) {
  const handleUpgrade = () => {
    if (onUpgrade) {
      onUpgrade();
    } else {
      // Default behavior: redirect to pricing page
      window.location.href = '/pricing';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="relative">
          <button
            onClick={onClose}
            className="absolute right-0 top-0 p-2 hover:bg-gray-100 rounded-full"
          >
            <X className="h-4 w-4" />
          </button>
          <div className="flex flex-col items-center space-y-4 pt-4">
            {/* Crown Icon */}
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 text-blue-600">
                <svg viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 6L9 9L6 6L9 3L12 6ZM18 6L15 9L12 6L15 3L18 6ZM12 6L15 9L18 6L15 3L12 6ZM6 6L9 9L6 12L3 9L6 6ZM18 6L21 9L18 12L15 9L18 6ZM12 18L9 15L12 12L15 15L12 18Z"/>
                </svg>
              </div>
            </div>
            
            <DialogTitle className="text-xl font-semibold text-center">
              Upgrade to Generate More Images Once
            </DialogTitle>
          </div>
        </DialogHeader>

        <div className="space-y-6 px-6 pb-6">
          {/* Feature Comparison */}
          <div className="flex items-center justify-center space-x-8">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">Max 1 🖼️</div>
              <div className="text-sm text-gray-500">Free</div>
            </div>
            <div className="text-gray-400">→</div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">Max 5 🖼️</div>
              <div className="text-sm text-blue-600">Premium</div>
            </div>
          </div>

          {/* Features List */}
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2L15 9H22L17 14L19 21L12 17L5 21L7 14L2 9H9L12 2Z"/>
                </svg>
              </div>
              <div>
                <div className="font-medium">More Credits</div>
                <div className="text-sm text-gray-500">Generate more images with increased credit allowance</div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2L15 9H22L17 14L19 21L12 17L5 21L7 14L2 9H9L12 2Z"/>
                </svg>
              </div>
              <div>
                <div className="font-medium">Priority Access</div>
                <div className="text-sm text-gray-500">Get first access to new features and faster generation</div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-blue-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <div>
                <div className="font-medium">No Watermarks</div>
                <div className="text-sm text-gray-500">Remove watermarks from all generated images & videos</div>
              </div>
            </div>
          </div>

          {/* Upgrade Button */}
          <Button 
            onClick={handleUpgrade}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg font-medium"
          >
            Upgrade to Premium
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
