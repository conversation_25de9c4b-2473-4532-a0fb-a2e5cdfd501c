"use client";

import Script from "next/script";

export default function PlausibleAnalytics() {
  if (process.env.NODE_ENV !== "production") {
    return null;
  }

  return (
    <Script 
      defer 
      data-domain="flux-krea.dev" 
      src="https://plausible.nancook.com/js/script.file-downloads.hash.outbound-links.pageview-props.revenue.tagged-events.js"
      strategy="afterInteractive"
    />
  );
} 