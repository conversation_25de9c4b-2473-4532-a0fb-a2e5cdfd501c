你是一个SEO专家，有着丰富的SEO经验，现在保证现有的布局不变，不要私自修改现有的代码，现在我需要给网站增加一个内页，请你帮我新建一个内页，slug是：http://localhost:3000/flux-1-krea-photorealistic
这个页面的核心关键词是：flux.1 krea photorealistic
相关关键词是：
flux.1 krea
flux.1 krea dev
flux.1 krea [Dev]
flux krea

这些相关关键词需要至少一次在页面中出现，要融入行业术语和专业词汇，提升内容权威性.
【严格确保主关键词flux.1 krea photorealistic的密度在3%-6%左右】

深度策略分析
 - 品牌DNA解析
 - 用户痛点映射
 - 价值主张提炼
 - 差异化定位
在页面中需要出现至少一次主页关键词：flux krea，链接到首页
【这是一个英文网站，不能出现中文，始终要显示英文】

请你根据下方的信息，帮我设计一个内页，需要用到图片的时候可以使用图片占位符，我后面自己再手动添加图片，在Hero部分要有按钮引导用户点击到首页使用这个模型生成图片。

在设计页面结构内容之前要先写出文章结构大纲，然后按照大纲来构建页面.

这是给你阅读的相关信息：
FLUX.1-Krea [dev] 经过蒸馏，在保留审美与真实感的同时，达到与 Krea 1 一致的质量。
与大多数图像模型不同，FLUX.1 Krea 带着鲜明的审美立场诞生。我们致力于塑造一款真正契合自身美学偏好的模型。

破除“AI感”
“一旦某项指标成为目标，它就不再是一项好指标。”  ——查尔斯·古德哈特

图像生成技术已从早期 GAN 只能产出模糊的猫和花，发展到如今能生成连贯的人脸、四肢与手势，能精准理解数量、渲染复杂文字，甚至让宇航员骑上骏马。
然而，使用 AI 生成图像时，一个明显趋势是它们独有的“AI 痕迹”：背景过度虚化、肤质蜡感僵硬、构图单调乏味……这些通病共同构成了所谓的“AI感”。

人们往往只关注模型有多“聪明”。我们常见用户抛出复杂提示来测试：它能让马骑宇航员吗？能把酒杯倒满吗？能正确渲染文字吗？多年来，我们设计了各种基准，把这些问题量化为具体指标。研究社区在提升生成能力方面成绩斐然。然而，在追求技术指标和基准优化的过程中，早期图像模型那种粗粝真实的外观、多元的风格以及富有创意的混搭，却被渐渐抛在了脑后。

我们的初衷很简单：“让 AI 生成的图像看起来不像 AI 生成的。”

作为生成式 AI 的使用者，我们希望打造一个真正解决这些问题的模型。不幸的是，许多学术领域常用的基准评估指标，实际上与用户真正关心的效果并不一致。

在预训练阶段，像 Fréchet Inception Distance（FID）和 CLIP Score 这样的指标在衡量模型整体性能方面是有用的——因为这个阶段生成的图像往往还不够连贯。而在预训练之后，DPG、GenEval、T2I-CompBench 和 GenAI-Bench 等评估基准在学术界和业界广泛使用，用于测试模型对提示词的遵循程度，如空间关系、属性绑定、物体数量等方面。

但在审美评价方面，常用的模型包括 LAION-Aesthetics、Pickscore、ImageReward 和 HPSv2。这些模型大多数是基于 CLIP 微调而来，而 CLIP 本身只能处理 224×224 分辨率的图像，参数量也较小。随着图像生成模型能力的不断提升，这些较老的审美打分模型已不足以胜任高质量评估任务。

举例来说，我们发现 LAION Aesthetics —— 虽然常被用来筛选高质量训练图像 —— 却高度偏好女性形象、模糊背景、过于柔和的纹理和高亮度图像。虽然这些审美评分器和图像质量过滤器在剔除低质量图像时是有用的，但若过度依赖它们来构建训练集，将会在模型训练中引入隐含的审美偏差。

尽管已经开始出现基于视觉语言模型的更先进的审美评分器，但问题依然存在：人类的偏好和审美高度个性化，无法轻易地简化为一个评分数字。
在提升模型能力的同时，若要避免倒退回“AI 感”的视觉风格，就必须对训练数据进行精细筛选，并对模型输出进行充分的校准与调控。

模式崩溃的艺术
“雕像在我动手之前就已经完整地存在于那块大理石之中。我所要做的，只是把多余的部分凿掉。”——米开朗基罗

训练一个图像生成模型大致可以分为两个阶段：预训练和后训练。模型的大部分美学能力是在后训练阶段学到的。但在解释我们的后训练方法之前，我们先来谈谈我们对这两个训练阶段的一些直觉理解。

预训练阶段
预训练阶段的重点应放在 “模式覆盖”（mode coverage） 和 “世界理解”（world understanding） 上。在这个阶段，我们为模型提供丰富的视觉世界知识：风格、物体、场景、人类等，其目标是最大化多样性。

我们甚至认为，即使数据是“差”的，只要这些不理想之处能够被模型准确地学习进条件输入中，依然是有价值的。事实上，除了教会模型我们想要的东西，我们也希望它理解我们不想要的东西。

许多图像生成流程都会使用负面提示词（negative prompts），如“太多手指、脸部畸形、模糊、过度饱和”等来提升图像质量。但要让这些负面提示有效地引导模型避开数据分布中的“劣质部分”，前提是：模型本身已经看过这些“坏图像”的样子。如果模型从未见过差图像，仅靠负面提示是无法发挥作用的。

虽然后训练阶段对模型最终质量的影响最大，但我们必须记住：模型的质量上限以及风格多样性，实际上来源于预训练阶段。

后训练阶段
在后训练阶段，我们的重点是调整并去除数据分布中不理想的部分。一个完成预训练的模型虽然具备广泛概念理解和输出多样性的能力，但它缺乏对美学输出的偏向性，因此难以稳定地产出高质量图像。这正是我们要引入 “模式收缩”（mode collapse） 的地方：我们希望开始有意识地引导模型偏向我们认为“理想”的分布区域，而不是均衡地保留所有类型的输出。

在 RLHF 阶段，我们应用了一种偏好优化技术的变体，称为 TPO（Tuned Preference Optimization），用于进一步提升模型的审美质量与风格化表现。我们使用的是高质量的内部偏好数据，这些数据都经过严格筛选，以确保数据的准确性与可靠性。在许多情况下，我们还会进行多轮偏好优化，进一步细化模型输出的风格与品质。

在探索各种后训练技术的过程中，我们也获得了一些关键发现，接下来我们将分享其中的几点。

质量胜于数量
在后训练中，所需的数据量其实比你想象的要少（不到 100 万张）。虽然数据量越大越有助于模型的稳定性和偏差的缓解，但数据的质量始终是最关键的。这一观察结果也与已有研究文献相符，这些文献指出：使用小规模、精心挑选的数据集进行训练，依然可以达到极佳效果。

我们的偏好标签数据是由熟悉当前模型能力和局限性的标注者精心收集的。他们非常清楚模型在哪些方面表现良好，哪些方面仍有待提升。尤其在偏好标注界面中，我们确保图像内容足够多样化，以获得聚焦而有代表性的标注结果。

采取“有主见”的训练方式
目前已有许多开源的偏好数据集，常用于评估偏好微调技术。在探索阶段，这些数据集在测试不同方法时确实非常有帮助。但我们发现，直接使用这些现有数据集进行训练，往往会带来一些不良的副作用，例如：

偏向于对称、简单的构图
模糊、过度柔和的纹理
色彩风格趋于单一
回退至典型的 “AI 风格”
我们认为：基于“全球性”用户偏好训练出来的模型，在审美表达上往往是次优的。对于诸如文字渲染、结构、解剖、提示词遵循度等客观性较强的目标，数据的多样性与规模确实非常重要；但对于“审美”这类主观性极强的目标来说，混合不同的审美倾向反而是有害的——某种程度上，它甚至像是一种“对抗”行为。

基于这一直觉，我们决定以一种非常“有主见”的方式来收集偏好数据，使其与我们的审美品味和明确的艺术方向保持一致。事实证明，将模型过拟合到某种特定风格，往往效果更好、实现也更容易。

未来研究方向
作为一家以产品为核心的公司，我们致力于构建直观、愉悦的生成式模型交互体验。Krea 1 只是我们迈出的第一步，旨在为创意工作者提供他们一直渴望的审美标准与画质。此次开源 FLUX.1 Krea [dev]，我们热切期待开源社区在此基础上带来的创新。

接下来，我们将持续提升模型的核心能力，并拓展至更多视觉领域，让用户能够自由探索、融合、混搭丰富多样的视觉内容。

本次工作是我们美学研究的起点。我们已经构建了一个带有鲜明审美取向的模型，但更长远的目标是打造真正“懂你”的个性化系统。在未来的研究中，通过个性化、美学与可控性的深入探索，我们希望为你带来一款与你的品味完美契合、并能不断精修作品的模型。



