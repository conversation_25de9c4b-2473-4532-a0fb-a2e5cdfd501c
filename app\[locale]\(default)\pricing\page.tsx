import { Button } from "@/components/ui/button";
import Pricing from "@/components/blocks/pricing";
import { getLandingPage } from "@/services/page";
import Link from "next/link";

export const runtime = "edge";

export async function generateMetadata({
  params: { locale },
}: {
  params: { locale: string };
}) {
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/pricing`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/pricing`;
  }

  return {
    title: "Flux Krea Pricing Plans",
    description: "Choose the right Flux Krea plan for your image editing needs",
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

export default async function PricePage({
  params: { locale },
}: {
  params: { locale: string };
}) {
  const page = await getLandingPage(locale);
  const { pricing } = page;

  return (
    <div className="container py-12 space-y-8">
      <div className="flex items-center justify-between">
        <Link href="/">
          <Button variant="outline" className="flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-left">
              <path d="m12 19-7-7 7-7" />
              <path d="M19 12H5" />
            </svg>
            Back to Home
          </Button>
        </Link>
      </div>

      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">Flux Krea Pricing Plans</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Choose the plan that's right for you and start creating amazing AI-powered image with Flux Krea
        </p>
      </div>

      {/* 使用原有的Pricing组件，保持样式一致 */}
      <Pricing pricing={pricing} />
    </div>
  );
} 