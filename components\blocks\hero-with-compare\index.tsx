"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import HappyUsers from "../hero/happy-users";
import HeroBg from "../hero/bg";
import { Hero as HeroType } from "@/types/blocks/hero";
import Icon from "@/components/icon";
import Link from "next/link";
import { ImageCompare } from "@/components/image-compare";

interface HeroWithCompareProps {
  hero: HeroType;
  compareImages?: {
    originalSrc: string;
    modifiedSrc: string;
    beforeText?: string;
    afterText?: string;
  };
}

export default function HeroWithCompare({ 
  hero, 
  compareImages = {
    originalSrc: "/imgs/compare/original.svg",
    modifiedSrc: "/imgs/compare/ghibli-style.svg",
    beforeText: "Original",
    afterText: "Edited with Kontext Dev"
  }
}: HeroWithCompareProps) {
  if (hero.disabled) {
    return null;
  }

  const highlightText = hero.highlight_text;
  let texts = null;
  if (highlightText) {
    texts = hero.title?.split(highlightText, 2);
  }

  return (
    <div className="relative overflow-hidden min-h-screen">
      <HeroBg />
      <section className="py-4 md:py-8 relative z-10">
        <div className="container">
          {hero.show_badge && (
            <div className="flex items-center justify-center mb-8">
              <img
                src="/imgs/badges/phdaily.svg"
                alt="phdaily"
                className="h-10 object-cover"
              />
            </div>
          )}
          <div className="text-center">
            {hero.announcement && (
              <a
                href={hero.announcement.url}
                className="mx-auto mb-3 inline-flex items-center gap-3 rounded-full border px-2 py-1 text-sm"
              >
                {hero.announcement.label && (
                  <Badge>{hero.announcement.label}</Badge>
                )}
                {hero.announcement.title}
              </a>
            )}

            {texts && texts.length > 1 ? (
              <h1 className="mx-auto mb-2 mt-1 md:mb-3 md:mt-2 max-w-3xl text-balance text-3xl font-bold lg:mb-7 lg:text-5xl">
                {texts[0]}
                <span className="bg-gradient-to-r from-primary via-primary to-primary bg-clip-text text-transparent">
                  {highlightText}
                </span>
                {texts[1]}
              </h1>
            ) : (
              <h1 className="mx-auto mb-2 mt-1 md:mb-3 md:mt-2 max-w-3xl text-balance text-3xl font-bold lg:mb-7 lg:text-5xl">
                {hero.title}
              </h1>
            )}

            <p
              className="m mx-auto max-w-3xl text-muted-foreground lg:text-xl"
              dangerouslySetInnerHTML={{ __html: hero.description || "" }}
            />
            
            {/* Image Comparison Component */}
            <div className="mx-auto mt-8 mb-8 w-full">
              <ImageCompare 
                originalSrc={compareImages.originalSrc}
                modifiedSrc={compareImages.modifiedSrc}
                alt="Photo restoration comparison"
                beforeText={compareImages.beforeText}
                afterText={compareImages.afterText}
                className="ghibli-card"
              />
              <p className="mt-4 text-muted-foreground">
               Experience generating realistic photos of different scenes with <Link href="/" className="text-primary hover:underline">Flux Krea</Link>.
              </p>
            </div>
            
            {hero.buttons && (
              <div className="mt-8 flex flex-col justify-center gap-4 sm:flex-row">
                {hero.buttons.map((item, i) => {
                  return (
                    <Link
                      key={i}
                      href={item.url || ""}
                      target={item.target || ""}
                      className="flex items-center"
                    >
                      <Button
                        className="w-full ghibli-button"
                        size="lg"
                        variant={item.variant || "default"}
                      >
                        {item.title}
                        {item.icon && (
                          <Icon name={item.icon} className="ml-1" />
                        )}
                      </Button>
                    </Link>
                  );
                })}
              </div>
            )}
            {hero.tip && (
              <p className="mt-8 text-md text-muted-foreground">{hero.tip}</p>
            )}
            {hero.show_happy_users && <HappyUsers />}
          </div>
        </div>
      </section>
    </div>
  );
} 